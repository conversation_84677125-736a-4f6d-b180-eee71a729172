# 项目简介

## 概述

本项目是一个基于Go语言开发的高性能MQTT协议编解码库，专为现代高并发场景设计。该库深度集成了gnet网络库和ants协程池，通过零拷贝技术、无锁设计和高效对象复用等先进技术，实现了极低的内存占用和卓越的处理性能，有效减少GC压力，显著提升系统整体性能。

## 核心特性

- 🚀 **极致性能**: 零拷贝技术 + 无锁设计，最大化处理效率
- 💾 **低内存占用**: 智能对象池和内存复用，最小化内存分配
- 🔗 **完美集成**: 与gnet事件驱动模型和ants协程池无缝融合
- 📋 **全协议支持**: 完整支持MQTT v3.1、v3.1.1和v5.0规范
- 🔧 **高度可扩展**: 模块化架构设计，便于扩展和定制

# 设计目标

- **高效处理**: 利用零拷贝技术和无锁设计最大化处理效率
- **低内存占用**: 通过对象池和内存复用最小化内存分配
- **与gnet融合**: 完美适配gnet的事件驱动模型
- **与ants融合**: 利用ants提供的goroutine池降低并发开销
- **完整支持**: 支持MQTT 3.1、3.1.1和5.0协议规范
- **可扩展性**: 模块化设计便于扩展和定制

# 协议功能支持

- **多版本兼容**: 完整支持MQTT v3.1、v3.1.1和v5.0
- **QoS等级**: 支持QoS 0、QoS 1和QoS 2消息传递
- **会话管理**: 支持持久会话和离线消息存储
- **保留消息**: 支持消息保留机制
- **遗嘱消息**: 支持客户端异常断开时的遗嘱通知
- **共享订阅**: 支持MQTT 5.0的共享订阅功能

# 系统架构

整体架构分为以下几个核心模块：

1. **编解码核心(codec)**: 负责MQTT数据包的序列化和反序列化
2. **对象池管理(pool)**: 维护消息对象池，减少GC压力
3. **缓冲区管理(buffer)**: 实现零拷贝的缓冲区管理
4. **连接管理(conn)**: 与gnet集成的连接抽象
5. **工作调度(worker)**: 与ants集成的工作调度系统

```
┌─────────────────────────────────────────┐
│              应用层                      │
└───────────────┬─────────────────────────┘
                │
┌───────────────▼─────────────────────────┐
│              MQTT库                      │
│  ┌─────────┐  ┌─────────┐  ┌─────────┐  │
│  │  编解码  │  │ 对象池  │  │ 缓冲区  │  │
│  └─────────┘  └─────────┘  └─────────┘  │
│  ┌─────────┐  ┌─────────┐               │
│  │连接管理 │  │工作调度 │               │
│  └─────────┘  └─────────┘               │
└───────────────┬─────────────────────────┘
                │
┌───────────────▼─────────────────────────┐
│        gnet网络库    ants协程池          │
└─────────────────────────────────────────┘
```
